# 导入必要的库
import os
import numpy as np
import pandas as pd
import geopandas as gpd
import rasterio
from rasterio.features import rasterize
from rasterio.mask import mask
from rasterio.transform import from_bounds
from shapely.geometry import box
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("库导入完成！")

# 数据路径设置
# 输入数据路径
world_boundaries_path = r"Z:\yuan\paper3_new02\shp\World_Country_Boundaries_GS(2021)6375\World_Country_Boundaries.shp"
basins_path = r"Z:\yuan\paper3_new02\shp\basins_lev01-12\basins_lev05_new.shp"
raster_path = r"Z:\yuan\ERA5-Land\fbl_025\EPEs\yz_600_1440\pre_yz_90_1971_2020.tif"

# 输出路径
output_dir = r"Z:\yuan\paper3_new02\shp\basin_Country"

# 创建输出目录
os.makedirs(output_dir, exist_ok=True)

print(f"输出目录已创建：{output_dir}")
print("\n数据路径设置完成！")

# 读取世界国家边界数据
print("正在读取世界国家边界数据...")
world_countries = gpd.read_file(world_boundaries_path)
print(f"世界国家边界数据读取完成，共{len(world_countries)}个国家")
print(f"国家边界坐标系：{world_countries.crs}")
print(f"国家ID字段：NR_C_ID")
print(f"国家边界数据列：{list(world_countries.columns)}")
print("\n前5个国家信息：")
print(world_countries[['NR_C_ID', 'geometry']].head())

# 读取研究区流域数据
print("正在读取研究区流域数据...")
basins = gpd.read_file(basins_path)
print(f"流域数据读取完成，共{len(basins)}个流域")
print(f"流域坐标系：{basins.crs}")
print(f"流域ID字段：HYBAS_ID")
print(f"流域数据列：{list(basins.columns)}")
print("\n前5个流域信息：")
print(basins[['HYBAS_ID', 'geometry']].head())

# 读取栅格数据
print("正在读取栅格数据...")
with rasterio.open(raster_path) as src:
    raster_data = src.read(1)  # 读取第一个波段
    raster_transform = src.transform
    raster_crs = src.crs
    raster_bounds = src.bounds
    raster_shape = raster_data.shape
    
print(f"栅格数据读取完成")
print(f"栅格坐标系：{raster_crs}")
print(f"栅格形状：{raster_shape}")
print(f"栅格范围：{raster_bounds}")
print(f"栅格变换参数：{raster_transform}")

# 统计有效像元（>=0）
valid_pixels = raster_data >= 0
valid_count = np.sum(valid_pixels)
total_count = raster_data.size
print(f"\n栅格统计：")
print(f"总像元数：{total_count}")
print(f"有效像元数（>=0）：{valid_count}")
print(f"有效像元比例：{valid_count/total_count:.2%}")
print(f"栅格数据范围：{np.nanmin(raster_data):.2f} ~ {np.nanmax(raster_data):.2f}")

# 检查和统一坐标系统
print("检查坐标系统...")
print(f"世界国家边界坐标系：{world_countries.crs}")
print(f"流域坐标系：{basins.crs}")
print(f"栅格坐标系：{raster_crs}")

# 选择合适的投影坐标系进行面积计算
# 使用等面积投影坐标系（Mollweide投影）来准确计算面积
area_crs = 'ESRI:54009'  # Mollweide投影，适合全球面积计算
print(f"\n使用投影坐标系进行面积计算：{area_crs}")

# 转换到面积计算坐标系
world_countries_area = world_countries.to_crs(area_crs)
basins_area = basins.to_crs(area_crs)

# 保持原始坐标系用于栅格操作
target_crs = raster_crs  # 以栅格数据的坐标系为准

if world_countries.crs != target_crs:
    print(f"转换世界国家边界坐标系从 {world_countries.crs} 到 {target_crs}")
    world_countries = world_countries.to_crs(target_crs)
    
if basins.crs != target_crs:
    print(f"转换流域坐标系从 {basins.crs} 到 {target_crs}")
    basins = basins.to_crs(target_crs)

print("\n坐标系统统一完成！")

# 创建研究区的总边界（所有流域的联合边界）
print("创建研究区总边界...")
try:
    # 尝试使用新的方法
    study_area = basins.union_all()
    study_area_area = basins_area.union_all()  # 用于面积计算的版本
except AttributeError:
    # 如果新方法不可用，使用旧方法
    study_area = basins.unary_union
    study_area_area = basins_area.unary_union  # 用于面积计算的版本
    
study_area_gdf = gpd.GeoDataFrame({'id': [1]}, geometry=[study_area], crs=basins.crs)
print(f"研究区总边界创建完成")
print(f"研究区面积：{study_area_area.area/1e6:.2f} km²")

# 保存研究区边界
study_area_path = os.path.join(output_dir, "study_area_boundary.shp")
study_area_gdf.to_file(study_area_path)
print(f"研究区边界已保存：{study_area_path}")

# 找出与研究区相交的国家
print("\n查找与研究区相交的国家...")
# 使用空间索引加速查询
intersecting_countries = world_countries[world_countries.intersects(study_area)]

print(f"找到{len(intersecting_countries)}个与研究区相交的国家")
print("\n相交国家列表：")
if 'NAME' in intersecting_countries.columns:
    for idx, row in intersecting_countries.iterrows():
        print(f"国家ID: {row['NR_C_ID']}, 国家名: {row.get('NAME', 'Unknown')}")
else:
    for idx, row in intersecting_countries.iterrows():
        print(f"国家ID: {row['NR_C_ID']}")

# 保存相交的国家边界
intersecting_countries_path = os.path.join(output_dir, "intersecting_countries.shp")
intersecting_countries.to_file(intersecting_countries_path)
print(f"\n相交国家边界已保存：{intersecting_countries_path}")

# 可视化研究区和相交国家
print("\n创建可视化图...")
fig, ax = plt.subplots(1, 1, figsize=(15, 10))

# 绘制相交的国家边界
intersecting_countries.plot(ax=ax, color='lightblue', edgecolor='blue', alpha=0.7, label='相交国家')

# 绘制研究区流域
basins.plot(ax=ax, color='red', alpha=0.5, edgecolor='darkred', label='研究区流域')

# 绘制研究区总边界
study_area_gdf.plot(ax=ax, color='none', edgecolor='black', linewidth=2, label='研究区边界')

ax.set_title('研究区与相交国家分布图', fontsize=16)
ax.legend()
ax.set_xlabel('经度')
ax.set_ylabel('纬度')

# 保存图片
fig_path = os.path.join(output_dir, "study_area_countries_overview.png")
plt.savefig(fig_path, dpi=300, bbox_inches='tight')
plt.show()
print(f"可视化图已保存：{fig_path}")

# 重新计算国家与研究区重叠面积（使用正确的投影坐标系）
print("重新计算国家与研究区重叠面积（使用投影坐标系）...")

# 使用等面积投影坐标系进行面积计算
area_crs = 'ESRI:54009'  # Mollweide投影
print(f"使用投影坐标系：{area_crs}")

# 转换相交国家到面积计算坐标系
intersecting_countries_area = intersecting_countries.to_crs(area_crs)

# 转换研究区到面积计算坐标系
try:
    study_area_area = basins.to_crs(area_crs).union_all()
except AttributeError:
    study_area_area = basins.to_crs(area_crs).unary_union

print(f"研究区总面积：{study_area_area.area/1e6:.2f} km²")

# 重新计算重叠面积
overlap_results_corrected = []

for i, (idx, country) in enumerate(intersecting_countries_area.iterrows()):
    # 从原始数据获取ID和名称
    original_country = intersecting_countries.iloc[i]
    country_id = original_country['NR_C_ID']
    country_name = original_country.get('NAME', f'Country_{country_id}')
    country_geom = country.geometry
    
    try:
        # 检查和修复几何体有效性
        if not country_geom.is_valid:
            country_geom = country_geom.buffer(0)
        
        if not study_area_area.is_valid:
            study_area_area = study_area_area.buffer(0)
        
        # 计算国家总面积（平方米）
        country_area = country_geom.area
        
        # 计算与研究区的交集
        try:
            intersection = country_geom.intersection(study_area_area)
        except Exception as e:
            print(f"国家 {country_id} 交集计算失败，尝试简化几何体: {e}")
            country_geom_simplified = country_geom.simplify(1000)  # 1km容差
            study_area_simplified = study_area_area.simplify(1000)
            intersection = country_geom_simplified.intersection(study_area_simplified)
        
        # 计算重叠面积（平方米）
        overlap_area = intersection.area if not intersection.is_empty else 0
        
        # 计算重叠比例
        overlap_ratio = overlap_area / country_area if country_area > 0 else 0
        
        # 转换为平方公里
        country_area_km2 = country_area / 1e6
        overlap_area_km2 = overlap_area / 1e6
        
        overlap_results_corrected.append({
            'Country_ID': country_id,
            'Country_Name': country_name,
            'Country_Area_km2': country_area_km2,
            'Overlap_Area_km2': overlap_area_km2,
            'Overlap_Ratio': overlap_ratio
        })
        
        print(f"国家ID {country_id} ({country_name}): "
              f"总面积 {country_area_km2:.2f} km², "
              f"重叠面积 {overlap_area_km2:.2f} km², "
              f"重叠比例 {overlap_ratio:.2%}")
              
    except Exception as e:
        print(f"处理国家 {country_id} ({country_name}) 时出错: {e}")
        overlap_results_corrected.append({
            'Country_ID': country_id,
            'Country_Name': country_name,
            'Country_Area_km2': 0,
            'Overlap_Area_km2': 0,
            'Overlap_Ratio': 0
        })

print(f"\n完成{len(overlap_results_corrected)}个国家的重叠面积计算（修正版）")

# 更新overlap_results变量
overlap_results = overlap_results_corrected

# 修复overlap_df，添加geometry列
print("修复overlap_df，跳过geometry相关操作...")

# 如果overlap_df中没有geometry列，添加一个空的geometry列
if 'geometry' not in overlap_df.columns:
    overlap_df['geometry'] = None
    print("已添加空的geometry列")

# 跳过创建重叠区域的GeoDataFrame
print("跳过重叠区域shapefile的创建")

# 显示统计摘要（使用修正后的研究区面积）
print("\n=== 重叠面积统计摘要 ===")
print(f"总研究区面积：{11602637.67:.2f} km²")  # 使用之前计算的正确面积
print(f"涉及国家数量：{len(overlap_df)}")
print(f"总重叠面积：{overlap_df['Overlap_Area_km2'].sum():.2f} km²")
print("\n前5个重叠面积最大的国家：")
print(overlap_df[['Country_ID', 'Country_Name', 'Overlap_Area_km2', 'Overlap_Ratio']].head())

# 导出重叠面积统计结果为CSV文件
print("\n=== 导出统计结果 ===")

# 确保output_dir变量已定义
if 'output_dir' not in locals():
    output_dir = r"Z:\yuan\paper3_new02\shp\basin_Country"

overlap_stats_path = os.path.join(output_dir, "country_overlap_statistics.csv")

try:
    # 选择需要导出的列，排除geometry列
    export_columns = ['Country_ID', 'Country_Name', 'Overlap_Area_km2', 'Overlap_Ratio']
    overlap_export_df = overlap_df[export_columns].copy()
    
    # 按重叠面积降序排序
    overlap_export_df = overlap_export_df.sort_values('Overlap_Area_km2', ascending=False)
    
    # 导出到CSV
    overlap_export_df.to_csv(overlap_stats_path, index=False, encoding='utf-8-sig')
    print(f"✓ 国家重叠面积统计结果已导出：{overlap_stats_path}")
    print(f"  包含 {len(overlap_export_df)} 个国家的重叠面积数据")
    
    # 显示导出文件的基本信息
    print(f"\n=== 导出文件信息 ===")
    print(f"文件路径：{overlap_stats_path}")
    print(f"包含列：{export_columns}")
    print(f"数据行数：{len(overlap_export_df)}")
    print(f"文件编码：UTF-8 with BOM（支持中文显示）")
    
    # 显示前几行数据预览
    print(f"\n前5行数据预览：")
    print(overlap_export_df.head())
    
except PermissionError:
    # 如果文件被占用，使用时间戳创建新文件名
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = os.path.join(output_dir, f"country_overlap_statistics_{timestamp}.csv")
    overlap_export_df.to_csv(backup_path, index=False, encoding='utf-8-sig')
    print(f"⚠️ 原文件被占用，统计结果已保存到：{backup_path}")
    print("请关闭可能打开该文件的程序（如Excel），然后重新运行以覆盖原文件。")
    
except Exception as e:
    print(f"❌ 导出CSV文件时出错：{e}")

# ===== 筛选重叠面积大于500km²的国家并导出为shp =====
print("\n" + "="*60)
print("开始筛选重叠面积大于500km²的国家并导出为shp文件")
print("="*60)

# 1. 筛选重叠面积大于500km²的国家
print("\n1. 筛选重叠面积大于500km²的国家...")
threshold_km2 = 500
filtered_countries = overlap_df[overlap_df['Overlap_Area_km2'] > threshold_km2].copy()

print(f"筛选前国家数量：{len(overlap_df)}")
print(f"筛选后国家数量：{len(filtered_countries)}")
print(f"筛选阈值：{threshold_km2} km²")

if len(filtered_countries) == 0:
    print("⚠️ 没有找到重叠面积大于500km²的国家")
else:
    print(f"\n筛选出的国家列表（按重叠面积降序）：")
    filtered_sorted = filtered_countries.sort_values('Overlap_Area_km2', ascending=False)
    for idx, row in filtered_sorted.iterrows():
        print(f"  {row['Country_ID']:>6} | {row['Country_Name']:<15} | {row['Overlap_Area_km2']:>12,.2f} km²")

# 2. 获取筛选国家的几何信息
print(f"\n2. 获取筛选国家的几何信息...")
filtered_country_ids = filtered_countries['Country_ID'].tolist()
print(f"需要获取几何信息的国家ID：{filtered_country_ids}")

# 从intersecting_countries中获取对应的几何信息
filtered_countries_with_geom = intersecting_countries[
    intersecting_countries['NR_C_ID'].isin(filtered_country_ids)
].copy()

print(f"成功获取几何信息的国家数量：{len(filtered_countries_with_geom)}")

# 3. 合并统计数据和几何信息
print(f"\n3. 合并统计数据和几何信息...")

# 将统计数据与几何数据进行合并
# 首先重命名列以便合并
filtered_countries_renamed = filtered_countries.rename(columns={'Country_ID': 'NR_C_ID'})

# 合并数据
final_gdf = filtered_countries_with_geom.merge(
    filtered_countries_renamed[['NR_C_ID', 'Country_Name', 'Country_Area_km2', 'Overlap_Area_km2', 'Overlap_Ratio']], 
    on='NR_C_ID', 
    how='inner'
)

print(f"合并后的GeoDataFrame包含 {len(final_gdf)} 个国家")
print(f"GeoDataFrame列：{list(final_gdf.columns)}")

# 4. 清理和准备导出数据
print(f"\n4. 准备导出数据...")

# 选择需要导出的列，确保列名符合shapefile规范（最多10个字符）
export_columns = {
    'NR_C_ID': 'COUNTRY_ID',
    'Country_Name': 'CTRY_NAME', 
    'Country_Area_km2': 'CTRY_AREA',
    'Overlap_Area_km2': 'OVLP_AREA',
    'Overlap_Ratio': 'OVLP_RATIO',
    'geometry': 'geometry'
}

# 创建导出用的GeoDataFrame
export_gdf = final_gdf[list(export_columns.keys())].copy()
export_gdf = export_gdf.rename(columns=export_columns)

# 确保数值列的数据类型正确
export_gdf['COUNTRY_ID'] = export_gdf['COUNTRY_ID'].astype(int)
export_gdf['CTRY_AREA'] = export_gdf['CTRY_AREA'].astype(float)
export_gdf['OVLP_AREA'] = export_gdf['OVLP_AREA'].astype(float)
export_gdf['OVLP_RATIO'] = export_gdf['OVLP_RATIO'].astype(float)

print(f"导出数据预览：")
print(export_gdf[['COUNTRY_ID', 'CTRY_NAME', 'OVLP_AREA']].head())

# 5. 导出为shapefile
print(f"\n5. 导出为shapefile...")

# 确保output_dir变量已定义
if 'output_dir' not in locals():
    output_dir = r"Z:\yuan\paper3_new02\shp\basin_Country"

# 定义输出文件路径
filtered_countries_shp_path = os.path.join(output_dir, "countries_overlap_gt500km2.shp")

try:
    # 导出为shapefile
    export_gdf.to_file(filtered_countries_shp_path, driver='ESRI Shapefile', encoding='utf-8')
    print(f"✓ 筛选后的国家边界已成功导出：{filtered_countries_shp_path}")
    print(f"  包含 {len(export_gdf)} 个重叠面积大于{threshold_km2}km²的国家")
    
    # 显示导出文件的基本信息
    print(f"\n=== 导出shapefile信息 ===")
    print(f"文件路径：{filtered_countries_shp_path}")
    print(f"包含字段：{list(export_gdf.columns)}")
    print(f"数据行数：{len(export_gdf)}")
    print(f"坐标系：{export_gdf.crs}")
    
    # 显示统计信息
    print(f"\n=== 筛选结果统计 ===")
    print(f"重叠面积总和：{export_gdf['OVLP_AREA'].sum():,.2f} km²")
    print(f"平均重叠面积：{export_gdf['OVLP_AREA'].mean():,.2f} km²")
    print(f"最大重叠面积：{export_gdf['OVLP_AREA'].max():,.2f} km²")
    print(f"最小重叠面积：{export_gdf['OVLP_AREA'].min():,.2f} km²")
    
    # 显示前几行数据预览
    print(f"\n导出数据详细预览：")
    display_df = export_gdf[['COUNTRY_ID', 'CTRY_NAME', 'OVLP_AREA', 'OVLP_RATIO']].sort_values('OVLP_AREA', ascending=False)
    print(display_df.to_string(index=False))
    
except Exception as e:
    print(f"❌ 导出shapefile时出错：{e}")
    print("请检查输出路径是否存在，以及是否有写入权限。")

# 6. 验证导出结果
print(f"\n6. 验证导出结果...")

try:
    # 重新读取导出的shapefile进行验证
    verification_gdf = gpd.read_file(filtered_countries_shp_path)
    
    print(f"✓ shapefile验证成功")
    print(f"  验证读取的记录数：{len(verification_gdf)}")
    print(f"  验证读取的字段：{list(verification_gdf.columns)}")
    print(f"  验证读取的坐标系：{verification_gdf.crs}")
    
    # 检查数据完整性
    if len(verification_gdf) == len(export_gdf):
        print(f"✓ 数据完整性验证通过：记录数匹配")
    else:
        print(f"⚠️ 数据完整性警告：导出{len(export_gdf)}条记录，验证读取{len(verification_gdf)}条记录")
    
    # 检查重叠面积数据
    min_overlap = verification_gdf['OVLP_AREA'].min()
    if min_overlap > threshold_km2:
        print(f"✓ 筛选条件验证通过：所有国家重叠面积都大于{threshold_km2}km²")
    else:
        print(f"⚠️ 筛选条件警告：发现重叠面积小于{threshold_km2}km²的记录（最小值：{min_overlap:.2f}km²）")
    
    print(f"\n=== 任务完成 ===")
    print(f"成功筛选并导出了{len(verification_gdf)}个重叠面积大于{threshold_km2}km²的国家")
    print(f"输出文件：{filtered_countries_shp_path}")
    
except Exception as e:
    print(f"❌ 验证导出结果时出错：{e}")



# 分析每个流域的有效像元数量和面积
print("分析每个流域的有效像元数量和面积...")

# 使用等面积投影坐标系计算流域面积
area_crs = 'ESRI:54009'  # Mollweide投影
basins_area = basins.to_crs(area_crs)

# 为每个流域创建掩膜并统计有效像元
basin_pixel_stats = []

# 打开栅格文件以获取完整的地理信息
with rasterio.open(raster_path) as src:
    for idx, basin in basins.iterrows():
        basin_id = basin['HYBAS_ID']
        basin_geom = basin.geometry
        
        # 计算流域面积（使用投影坐标系）
        basin_geom_area = basins_area.iloc[idx].geometry
        if not basin_geom_area.is_valid:
            basin_geom_area = basin_geom_area.buffer(0)
        basin_area_km2 = basin_geom_area.area / 1e6  # 转换为平方公里
        
        try:
            # 使用流域几何体裁剪栅格
            masked_data, masked_transform = mask(src, [basin_geom], crop=True, nodata=np.nan)
            masked_data = masked_data[0]  # 取第一个波段
            
            # 统计有效像元（>=0）
            valid_mask = masked_data >= 0
            valid_pixels_count = np.sum(valid_mask)
            total_pixels_count = np.sum(~np.isnan(masked_data))
            
            basin_pixel_stats.append({
                'Basin_ID': basin_id,
                'Basin_Area_km2': basin_area_km2,
                'Total_Pixels': total_pixels_count,
                'Valid_Pixels': valid_pixels_count,
                'Valid_Ratio': valid_pixels_count / total_pixels_count if total_pixels_count > 0 else 0
            })
            
            print(f"流域 {basin_id}: 面积 {basin_area_km2:.2f} km², "
                  f"总像元 {total_pixels_count}, 有效像元 {valid_pixels_count}, "
                  f"有效比例 {valid_pixels_count/total_pixels_count:.2%}" if total_pixels_count > 0 else f"流域 {basin_id}: 面积 {basin_area_km2:.2f} km², 无像元")
            
        except Exception as e:
            print(f"处理流域 {basin_id} 时出错: {e}")
            basin_pixel_stats.append({
                'Basin_ID': basin_id,
                'Basin_Area_km2': basin_area_km2,
                'Total_Pixels': 0,
                'Valid_Pixels': 0,
                'Valid_Ratio': 0
            })

print(f"\n完成{len(basin_pixel_stats)}个流域的像元和面积统计")

# 保存流域像元和面积统计结果
basin_stats_df = pd.DataFrame(basin_pixel_stats)
basin_stats_df = basin_stats_df.sort_values('Valid_Pixels', ascending=False)

basin_stats_path = os.path.join(output_dir, "basin_pixel_statistics.csv")
basin_stats_df.to_csv(basin_stats_path, index=False, encoding='utf-8-sig')
print(f"流域像元和面积统计结果已保存：{basin_stats_path}")

# 显示统计摘要
print("\n=== 流域像元和面积统计摘要 ===")
print(f"流域总数：{len(basin_stats_df)}")
print(f"总流域面积：{basin_stats_df['Basin_Area_km2'].sum():.2f} km²")
print(f"平均流域面积：{basin_stats_df['Basin_Area_km2'].mean():.2f} km²")
print(f"总有效像元数：{basin_stats_df['Valid_Pixels'].sum()}")
print(f"平均每个流域有效像元数：{basin_stats_df['Valid_Pixels'].mean():.0f}")
print("\n前5个有效像元最多的流域：")
print(basin_stats_df[['Basin_ID', 'Basin_Area_km2', 'Valid_Pixels', 'Valid_Ratio']].head())
print("\n前5个面积最大的流域：")
basin_stats_by_area = basin_stats_df.sort_values('Basin_Area_km2', ascending=False)
print(basin_stats_by_area[['Basin_ID', 'Basin_Area_km2', 'Valid_Pixels', 'Valid_Ratio']].head())

# 生成流域ID栅格（只在有效像元位置赋值流域ID）
print("生成流域ID栅格...")

with rasterio.open(raster_path) as src:
    # 创建输出栅格，初始化为nodata
    basin_id_raster = np.full(src.shape, src.nodata, dtype=np.float32)
    
    # 读取原始栅格数据
    original_data = src.read(1)
    
    # 只处理有效像元（>=0）
    valid_mask = original_data >= 0
    
    print(f"原始栅格中有效像元数量：{np.sum(valid_mask)}")
    
    # 为每个流域分配ID
    processed_pixels = 0
    
    for idx, basin in basins.iterrows():
        basin_id = basin['HYBAS_ID']
        basin_geom = basin.geometry
        
        try:
            # 创建流域掩膜
            basin_mask = rasterize(
                [basin_geom],
                out_shape=src.shape,
                transform=src.transform,
                fill=0,
                default_value=1,
                dtype=np.uint8
            )
            
            # 找到同时满足有效像元和流域范围的像元
            basin_valid_mask = valid_mask & (basin_mask == 1)
            basin_pixel_count = np.sum(basin_valid_mask)
            
            if basin_pixel_count > 0:
                # 在这些位置赋值流域ID
                basin_id_raster[basin_valid_mask] = basin_id
                processed_pixels += basin_pixel_count
                print(f"流域 {basin_id}: 分配了 {basin_pixel_count} 个有效像元")
            
        except Exception as e:
            print(f"处理流域 {basin_id} 时出错: {e}")
    
    print(f"\n总共处理了 {processed_pixels} 个有效像元")
    
    # 保存流域ID栅格
    basin_id_raster_path = os.path.join(output_dir, "basin_id_raster.tif")
    
    # 更新profile
    profile = src.profile.copy()
    profile.update({
        'dtype': rasterio.float32,
        'nodata': -9999,
        'compress': 'lzw'
    })
    
    # 将nodata值设置为-9999
    basin_id_raster[basin_id_raster == src.nodata] = -9999
    
    with rasterio.open(basin_id_raster_path, 'w', **profile) as dst:
        dst.write(basin_id_raster, 1)
    
    print(f"流域ID栅格已保存：{basin_id_raster_path}")
    
    # 统计结果
    unique_ids = np.unique(basin_id_raster[basin_id_raster != -9999])
    print(f"栅格中包含的流域ID数量：{len(unique_ids)}")
    print(f"流域ID范围：{unique_ids.min():.0f} - {unique_ids.max():.0f}")